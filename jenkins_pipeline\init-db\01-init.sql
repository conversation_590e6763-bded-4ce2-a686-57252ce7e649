-- Sample initialization script for smarthrms database
-- This file will be executed when the MySQL container starts for the first time

USE smarthrms;

-- Create a sample table (optional - remove if not needed)
-- CREATE TABLE IF NOT EXISTS users (
--     id BIGINT AUTO_INCREMENT PRIMARY KEY,
--     username VARCHAR(50) NOT NULL UNIQUE,
--     email VARCHAR(100) NOT NULL UNIQUE,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- Insert sample data (optional - remove if not needed)
-- INSERT INTO users (username, email) VALUES 
-- ('admin', '<EMAIL>'),
-- ('user1', '<EMAIL>');

-- Add your database initialization scripts here
SELECT 'Database smarthrms initialized successfully!' as message;
