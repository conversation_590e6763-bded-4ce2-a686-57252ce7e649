package org.devops


 // Simple email notification utilities
 
class NotificationUtils implements Serializable {
    def steps
    
    NotificationUtils(steps) {
        this.steps = steps
    }
    
    
     //Send success notification
     
    def sendSuccess(List recipients, String appName, String branch, String environment) {
        if (!recipients || recipients.isEmpty()) {
            steps.echo "No email recipients configured, skipping notification"
            return
        }
        
        def subject = "SUCCESS: ${appName} deployed to ${environment}"
        def body = """
Deployment completed successfully!

Application: ${appName}
Branch: ${branch}
Environment: ${environment}
Build: ${steps.env.BUILD_NUMBER}
        """.trim()
        
        sendEmail(recipients, subject, body)
    }
    
    
     //Send failure notification
     
    def sendFailure(List recipients, String appName, String branch, String environment, String error = "") {
        if (!recipients || recipients.isEmpty()) {
            steps.echo "No email recipients configured, skipping notification"
            return
        }
        
        def subject = "FAILED: ${appName} deployment to ${environment}"
        def body = """
Deployment failed!

Application: ${appName}
Branch: ${branch}
Environment: ${environment}
Build: ${steps.env.BUILD_NUMBER}
Error: ${error}
        """.trim()
        
        sendEmail(recipients, subject, body)
    }
    
    
     // Send email using Jenkins mail step
     
    private def sendEmail(List recipients, String subject, String body) {
        try {
            steps.mail(
                to: recipients.join(','),
                subject: subject,
                body: body
            )
            steps.echo "Email notification sent to: ${recipients.join(', ')}"
        } catch (Exception e) {
            steps.echo "Failed to send email notification: ${e.getMessage()}"
        }
    }
}
