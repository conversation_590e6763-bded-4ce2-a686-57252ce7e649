#!/bin/bash

# Test Docker Build Script for Spring Boot Application
# This script helps you test the Docker build locally before running the Jenkins pipeline

set -e  # Exit on any error

# Configuration
APP_NAME="spring-boot-demo"
IMAGE_NAME="techvg/spring-boot-demo"
TAG="test-$(date +%Y%m%d-%H%M%S)"
PORT="8089"

echo "=== Docker Build Test Script ==="
echo "App Name: $APP_NAME"
echo "Image Name: $IMAGE_NAME"
echo "Tag: $TAG"
echo "Port: $PORT"
echo "================================"

# Function to cleanup on exit
cleanup() {
    echo "Cleaning up..."
    docker stop "$APP_NAME-test" 2>/dev/null || true
    docker rm "$APP_NAME-test" 2>/dev/null || true
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Step 1: Build the Docker image
echo "Step 1: Building Docker image..."
docker build -t "$IMAGE_NAME:$TAG" .

if [ $? -eq 0 ]; then
    echo "✅ Docker image built successfully"
else
    echo "❌ Docker image build failed"
    exit 1
fi

# Step 2: Run the container
echo "Step 2: Running container..."
docker run -d --name "$APP_NAME-test" -p "$PORT:$PORT" "$IMAGE_NAME:$TAG"

if [ $? -eq 0 ]; then
    echo "✅ Container started successfully"
else
    echo "❌ Container failed to start"
    exit 1
fi

# Step 3: Wait for application to start
echo "Step 3: Waiting for application to start..."
sleep 30

# Step 4: Check if container is running
echo "Step 4: Checking container status..."
if docker ps | grep -q "$APP_NAME-test"; then
    echo "✅ Container is running"
    docker ps | grep "$APP_NAME-test"
else
    echo "❌ Container is not running"
    echo "Container logs:"
    docker logs "$APP_NAME-test"
    exit 1
fi

# Step 5: Test health check endpoint
echo "Step 5: Testing health check endpoint..."
if curl -f "http://localhost:$PORT/actuator/health" > /dev/null 2>&1; then
    echo "✅ Health check endpoint is accessible"
    echo "Health check response:"
    curl -s "http://localhost:$PORT/actuator/health" | jq . 2>/dev/null || curl -s "http://localhost:$PORT/actuator/health"
else
    echo "❌ Health check endpoint is not accessible"
    echo "Trying root endpoint..."
    if curl -f "http://localhost:$PORT/" > /dev/null 2>&1; then
        echo "✅ Root endpoint is accessible"
    else
        echo "❌ Application is not responding"
        echo "Container logs:"
        docker logs "$APP_NAME-test"
        exit 1
    fi
fi

# Step 6: Show container logs
echo "Step 6: Container logs (last 20 lines)..."
docker logs --tail 20 "$APP_NAME-test"

# Step 7: Test summary
echo ""
echo "=== Test Summary ==="
echo "✅ Docker image built successfully"
echo "✅ Container started successfully"
echo "✅ Application is running on port $PORT"
echo "✅ Health check endpoint is working"
echo ""
echo "You can now:"
echo "1. Test your application at: http://localhost:$PORT"
echo "2. Check health endpoint at: http://localhost:$PORT/actuator/health"
echo "3. View logs with: docker logs $APP_NAME-test"
echo ""
echo "When done testing, the container will be automatically cleaned up."
echo "Press Ctrl+C to stop and cleanup, or wait for manual cleanup."

# Keep the script running so user can test
echo "Container is running. Press Ctrl+C to stop and cleanup..."
while true; do
    sleep 10
    if ! docker ps | grep -q "$APP_NAME-test"; then
        echo "Container has stopped unexpectedly"
        docker logs "$APP_NAME-test"
        break
    fi
done
