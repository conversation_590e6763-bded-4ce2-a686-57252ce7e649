# Default configuration for <PERSON> Shared Library - Docker-based Deployment

# Application defaults
app:
  port: 8080
  healthEndpoints:
    springboot: "/actuator/health"
    angular: "/"

# Harbor registry configuration
harbor:
  registry: "harbor.company.com"
  credentialsId: "harbor-registry-credentials"

# Environment configuration by branch
environments:
  develop:
    name: "Development"
    servers: []
  release:
    name: "QA"
    servers: []
  tag:
    name: "Production"
    servers: []

# Build configuration - Commands are hardcoded in BuildUtils for simplicity

# Docker deployment configuration
deployment:
  ssh:
    options: "-o StrictHostKeyChecking=no"
    user: "jenkins"
  docker:
    containerPrefix: "app"
    restartPolicy: "unless-stopped"
  healthCheck:
    waitTime: 30        # Used in DeployUtils.performHealthCheck()
    maxRetries: 3       # Used in DeployUtils.healthCheckSingleServer()

# SonarQube configuration (commented out for now)
# sonar:
#   serverUrl: "http://sonarqube.company.com"
#   credentialsId: "sonarqube-token"

# Branch patterns are handled in code logic - no configuration needed

# Notification configuration
notifications:
  email:
    enabled: true
    defaultRecipients: []
