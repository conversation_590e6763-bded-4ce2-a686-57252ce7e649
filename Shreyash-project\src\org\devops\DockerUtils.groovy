package org.devops

class DockerUtils implements Serializable {
    def steps
    
    DockerUtils(steps) {
        this.steps = steps
    }
        
     // Build and push Docker image to Harbor registry
    def buildAndPushImage(String harborUrl, String imageName, String imageTag) {
        def fullImageName = "${harborUrl}/${imageName}:${imageTag}"

        steps.echo "Building Docker image: ${fullImageName}"

        // Build Docker image
        steps.sh "docker build -t ${fullImageName} ."

        // Push to Harbor registry
        pushToHarbor(harborUrl, imageName, imageTag)

        steps.echo "Docker image built and pushed successfully"
        return fullImageName
    }
    
     // Push image to Harbor registry with credentials
     
    private def pushToHarbor(String harborUrl, String imageName, String imageTag) {
        def fullImageName = "${harborUrl}/${imageName}:${imageTag}"
        
        steps.withCredentials([steps.usernamePassword(
            credentialsId: 'harbor-registry-credentials',
            usernameVariable: 'HARBOR_USER',
            passwordVariable: 'HARBOR_PASS'
        )]) {
            steps.sh "echo \$HARBOR_PASS | docker login ${harborUrl} -u \$HARBOR_USER --password-stdin"
            steps.sh "docker push ${fullImageName}"
            steps.sh "docker logout ${harborUrl}"
        }
    }
    
    
     // Pull Docker image from Harbor registry to target servers
     
    def pullImage(List servers, String fullImageName) {
        steps.echo "Pulling Docker image: ${fullImageName} on servers: ${servers.join(', ')}"

        def harborUrl = getRegistryFromImage(fullImageName)

        servers.each { server ->
            steps.echo "Pulling image on server: ${server}"
            steps.withCredentials([steps.usernamePassword(
                credentialsId: 'harbor-registry-credentials',
                usernameVariable: 'HARBOR_USER',
                passwordVariable: 'HARBOR_PASS'
            )]) {
                steps.sh """
                    ssh -o StrictHostKeyChecking=no jenkins@${server} '
                        echo "Logging into Harbor registry..."
                        echo \$HARBOR_PASS | docker login ${harborUrl} -u \$HARBOR_USER --password-stdin
                        echo "Pulling image: ${fullImageName}"
                        docker pull ${fullImageName}
                        docker logout ${harborUrl}
                    '
                """
            }
        }
        steps.echo "✓ Successfully pulled image on all servers"
    }

    
     // Extract registry URL from full image name
     
    private def getRegistryFromImage(String fullImageName) {
        return fullImageName.split('/')[0]
    }
    
    
    //Generate image tag based on branch and build number
     
    def generateImageTag(String branch, String buildNumber) {
        if (branch.startsWith("release/")) {
            return branch.replace("release/", "") + "-${buildNumber}"
        } else if (branch.startsWith("tag/")) {
            return branch.replace("tag/", "")
        } else if (branch == "develop") {
            return "develop-${buildNumber}"
        } else {
            return "${branch}-${buildNumber}".replaceAll("[^a-zA-Z0-9.-]", "-")
        }
    }

    // Unused utility methods removed - keeping only essential Docker operations
}
