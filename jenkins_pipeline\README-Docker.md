# Spring Boot Application with Docker

This guide explains how to run the Spring Boot application using <PERSON><PERSON> and Docker Compose.

## Prerequisites

- Docker installed on your system
- Docker Compose installed on your system

## Quick Start

1. **Build and run the application with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

2. **Access the application:**
   - Application: http://localhost:8089
   - Test endpoint: http://localhost:8089/api/ping
   - MySQL database: localhost:3306

## Docker Commands

### Using Docker Compose (Recommended)

```bash
# Build and start all services
docker-compose up --build

# Start services in background
docker-compose up -d

# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete database data)
docker-compose down -v

# View logs
docker-compose logs app
docker-compose logs mysql

# Rebuild only the app
docker-compose build app
```

### Using Docker directly

```bash
# Build the application image
docker build -t spring-boot-app .

# Run MySQL container
docker run -d \
  --name mysql-container \
  -e MYSQL_ROOT_PASSWORD=root \
  -e MYSQL_DATABASE=smarthrms \
  -p 3306:3306 \
  mysql:8.0

# Run the application container
docker run -d \
  --name app-container \
  --link mysql-container:mysql \
  -e SPRING_DATASOURCE_URL=********************************* \
  -p 8089:8089 \
  spring-boot-app
```

## Configuration

### Environment Variables

The application supports the following environment variables:

- `SPRING_DATASOURCE_URL`: Database connection URL
- `SPRING_DATASOURCE_USERNAME`: Database username
- `SPRING_DATASOURCE_PASSWORD`: Database password
- `SERVER_PORT`: Application port (default: 8089)

### Database Initialization

- Database initialization scripts can be placed in the `init-db/` directory
- These scripts will be executed when the MySQL container starts for the first time
- The sample `01-init.sql` file is provided as an example

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :8089
   # or
   lsof -i :8089
   ```

2. **Database connection issues:**
   - Ensure MySQL container is healthy: `docker-compose ps`
   - Check MySQL logs: `docker-compose logs mysql`
   - Verify database credentials in docker-compose.yml

3. **Application won't start:**
   - Check application logs: `docker-compose logs app`
   - Ensure all dependencies are properly configured in pom.xml

### Useful Commands

```bash
# Connect to MySQL container
docker-compose exec mysql mysql -u root -p smarthrms

# Connect to application container
docker-compose exec app bash

# Check container status
docker-compose ps

# Remove all containers and images
docker-compose down --rmi all
```

## Development

For development purposes, you can:

1. **Mount source code as volume** (add to docker-compose.yml):
   ```yaml
   volumes:
     - ./src:/app/src
   ```

2. **Enable hot reload** by adding Spring Boot DevTools dependency to pom.xml

3. **Use different profiles** by setting:
   ```yaml
   environment:
     SPRING_PROFILES_ACTIVE: dev
   ```

## Production Considerations

- Use specific image tags instead of `latest`
- Set up proper logging configuration
- Configure health checks
- Use secrets management for sensitive data
- Set resource limits for containers
- Use multi-stage Docker builds for smaller images
