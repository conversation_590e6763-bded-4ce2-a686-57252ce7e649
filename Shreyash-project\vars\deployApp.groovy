/**
 * Docker-based deployment pipeline for Spring Boot and Angular applications
 * Supports develop/release (build+deploy) and tag (deploy only) branches
 */
def call(Map config = [:]) {
    // Validate required parameters
    if (!config.appType) {
        error "appType is required (springboot or angular)"
    }
    if (!config.harborUrl) {
        error "harborUrl is required"
    }
    if (!config.imageName) {
        error "imageName is required"
    }

    def branch = env.BRANCH_NAME ?: 'develop'
    def appType = config.appType
    def imageName = config.imageName
    def harborUrl = config.harborUrl
    def port = config.port ?: 8080
    def skipTests = config.skipTests ?: false
    def servers = []
    def skipBuild = false

    // Determine target servers and behavior based on branch
    if (branch == 'develop') {
        servers = config.devServers ?: []
    } else if (branch.startsWith('release/')) {
        servers = config.qaServers ?: []
    } else if (branch.startsWith('tag/')) {
        servers = (config.preProdServers ?: []) + (config.prodServers ?: [])
        skipBuild = true
    } else {
        error "Unsupported branch pattern: ${branch}. Supported: develop, release/*, tag/*"
    }

    if (servers.isEmpty()) {
        error "No target servers configured for branch: ${branch}"
    }

    echo "=== Pipeline Configuration ==="
    echo "Branch: ${branch}"
    echo "App Type: ${appType}"
    echo "Image Name: ${imageName}"
    echo "Harbor URL: ${harborUrl}"
    echo "Port: ${port}"
    echo "Skip Tests: ${skipTests}"
    echo "Skip Build: ${skipBuild}"
    echo "Target Servers: ${servers.join(', ')}"
    echo "================================"

    // Execute pipeline workflow
    node {
        try {
            def environment = getEnvironmentName(branch)
            executeWorkflow(config, servers, skipBuild, skipTests, appType, imageName, harborUrl, port, environment)

            // Send success notification
            if (config.emailRecipients) {
                def notificationUtils = new org.devops.NotificationUtils(this)
                notificationUtils.sendSuccess(config.emailRecipients, imageName, branch, environment)
            }
        } catch (Exception e) {
            currentBuild.result = 'FAILURE'
            echo "Pipeline failed: ${e.getMessage()}"

            // Send failure notification
            if (config.emailRecipients) {
                def notificationUtils = new org.devops.NotificationUtils(this)
                def environment = getEnvironmentName(branch)
                notificationUtils.sendFailure(config.emailRecipients, imageName, branch, environment, e.getMessage())
            }

            throw e
        }
    }
}

/**
 * Get environment name based on branch
 */
def getEnvironmentName(String branch) {
    if (branch == 'develop') {
        return 'Development'
    } else if (branch.startsWith('release/')) {
        return 'QA'
    } else if (branch.startsWith('tag/')) {
        return 'Production'
    } else {
        return 'Unknown'
    }
}

/**
 * Execute the main workflow steps
 */
def executeWorkflow(config, servers, skipBuild, skipTests, appType, imageName, harborUrl, port, environment) {
    def buildUtils = new org.devops.BuildUtils(this)
    def dockerUtils = new org.devops.DockerUtils(this)
    def deployUtils = new org.devops.DeployUtils(this)

    stage('Checkout') {
        checkout scm
        echo "✓ Checked out source code from SCM"
    }

    if (!skipBuild) {
        // Commented out for now - SonarQube integration
        // stage('SonarQube Analysis') {
        //     buildUtils.runSonarAnalysis(config.sonarProjectKey)
        // }

        if (!skipTests) {
            stage('Run Tests') {
                buildUtils.runTests(appType)
            }
        } else {
            echo "⚠️ Tests skipped as requested"
        }

        stage('Build Application') {
            buildUtils.buildApplication(appType)
        }

        stage('Build & Push Docker Image') {
            def imageTag = dockerUtils.generateImageTag(env.BRANCH_NAME, env.BUILD_NUMBER)
            dockerUtils.buildAndPushImage(harborUrl, imageName, imageTag)
            env.IMAGE_TAG = imageTag
            echo "✓ Built and pushed image: ${harborUrl}/${imageName}:${imageTag}"
        }
    } else {
        // For tag deployments, use existing image from corresponding release
        def imageTag = env.BRANCH_NAME.replace('tag/', '')
        env.IMAGE_TAG = imageTag
        echo "📦 Using existing Docker image tag: ${imageTag}"
    }

    stage('Pull Docker Image') {
        def fullImageName = "${harborUrl}/${imageName}:${env.IMAGE_TAG}"
        dockerUtils.pullImage(servers, fullImageName)
    }

    stage('Deploy to Servers') {
        def fullImageName = "${harborUrl}/${imageName}:${env.IMAGE_TAG}"
        deployUtils.deployToServers(servers, fullImageName, port)
    }

    stage('Health Check') {
        def endpoint = (appType == 'springboot') ? '/actuator/health' : '/'
        deployUtils.performHealthCheck(servers, port, endpoint)
    }

    stage('Post-Deploy Status') {
        deployUtils.getContainerStatus(servers)
        echo "🎉 Deployment completed successfully to ${environment} environment!"
    }
}
