# Test Configuration - Remote MySQL Database
spring.application.name=dev-test

# Remote MySQL Database Configuration for Testing
spring.datasource.url=*************************************
spring.datasource.username=admin
spring.datasource.password=admin123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration for Testing
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

# Disable unnecessary features during testing
spring.jpa.open-in-view=false
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
