# Test configuration - uses H2 in-memory database for testing
spring.application.name=dev-test

# H2 in-memory database for tests
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA/Hibernate configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# Disable JPA repositories if not needed for tests
# spring.data.jpa.repositories.enabled=false
