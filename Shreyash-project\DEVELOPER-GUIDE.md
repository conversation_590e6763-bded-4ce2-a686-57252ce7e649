# 👨‍💻 <PERSON> Shared Library - Complete Developer Guide

This guide provides comprehensive information for developers using the Jenkins shared library for Docker-based deployment.

## 🎯 Quick Start

### Basic Usage
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

deployApp([
    appType: 'springboot',           // or 'angular'
    imageName: 'my-app',
    harborUrl: 'harbor.company.com',
    devServers: ['*********'],
    qaServers: ['*********'],
    prodServers: ['*********'],
    emailRecipients: ['<EMAIL>']
])
```

## 📋 Complete Parameter Reference

### Required Parameters
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `appType` | String | Application type | `'springboot'` or `'angular'` |
| `imageName` | String | Docker image name | `'my-spring-app'` |
| `harborUrl` | String | Harbor registry URL | `'harbor.company.com'` |

### Server Configuration
| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `devServers` | List | Development servers | `['*********', '*********']` |
| `qaServers` | List | QA/Test servers | `['*********']` |
| `preProdServers` | List | Pre-production servers | `['*********']` |
| `prodServers` | List | Production servers | `['*********', '*********']` |

### Optional Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `port` | Integer | `8080` | Application port |
| `skipTests` | Boolean | `false` | Skip test execution |
| `emailRecipients` | List | `[]` | Email notification list |

## 🌿 Branch-Based Deployment Strategy

### Branch Behavior
| Branch Pattern | Build | Test | Docker | Deploy Target | Description |
|---------------|-------|------|--------|---------------|-------------|
| `develop` | ✅ | ✅ | ✅ | `devServers` | Full pipeline to development |
| `release/x.y.z` | ✅ | ✅ | ✅ | `qaServers` | Full pipeline to QA |
| `tag/x.y.z` | ❌ | ❌ | ❌ | `preProdServers` + `prodServers` | Deploy existing image |

### Image Tagging Strategy
```groovy
// Branch: develop → Tag: develop-123
// Branch: release/1.2.0 → Tag: 1.2.0-123
// Branch: tag/1.2.0 → Tag: 1.2.0 (reuses existing)
// Branch: feature/new-ui → Tag: feature-new-ui-123
```

## 🏗️ Application Types

### Spring Boot Applications

#### Requirements
- **Build Tool**: Maven with `pom.xml`
- **Java Version**: 17+ recommended
- **Dockerfile**: Must be in project root
- **Health Endpoint**: `/actuator/health` (Spring Boot Actuator)

#### Sample Project Structure
```
my-spring-app/
├── src/
│   └── main/
│       ├── java/
│       └── resources/
│           └── application.yml
├── pom.xml
├── Dockerfile
├── Jenkinsfile
└── README.md
```

#### Sample pom.xml Dependencies
```xml
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
</dependencies>
```

#### Sample application.yml
```yaml
server:
  port: 8080

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

spring:
  application:
    name: my-spring-app
```

#### Build Process
```bash
# Maven wrapper detection and execution
./mvnw clean package -DskipTests  # Build
./mvnw test                       # Test
```

### Angular Applications

#### Requirements
- **Build Tool**: npm with `package.json`
- **Node Version**: 16+ recommended
- **Dockerfile**: Must be in project root
- **Health Endpoint**: `/` (root path)

#### Sample Project Structure
```
my-angular-app/
├── src/
│   ├── app/
│   ├── assets/
│   └── environments/
├── package.json
├── angular.json
├── Dockerfile
├── nginx.conf
├── Jenkinsfile
└── README.md
```

#### Sample package.json Scripts
```json
{
  "scripts": {
    "build": "ng build",
    "build:prod": "ng build --configuration production",
    "test": "ng test",
    "test:ci": "ng test --watch=false --browsers=ChromeHeadless"
  }
}
```

#### Sample nginx.conf
```nginx
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

#### Build Process
```bash
npm install                                    # Install dependencies
npm run build --prod                         # Build
npm test -- --watch=false --browsers=ChromeHeadless  # Test
```

## 🐳 Docker Configuration

### Spring Boot Dockerfile Template
```dockerfile
FROM openjdk:17-jre-slim

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

WORKDIR /app
COPY target/*.jar app.jar

# Change ownership to non-root user
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### Angular Dockerfile Template
```dockerfile
# Build stage
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build --prod

# Runtime stage
FROM nginx:alpine
RUN apk add --no-cache curl

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application
COPY --from=builder /app/dist/* /usr/share/nginx/html/

# Create non-root user
RUN addgroup -g 1001 -S appuser && adduser -u 1001 -S appuser -G appuser
RUN chown -R appuser:appuser /var/cache/nginx /var/run /var/log/nginx /usr/share/nginx/html

USER appuser
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:80/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

## 📝 Jenkinsfile Examples

### Basic Spring Boot Pipeline
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

properties([
    buildDiscarder(logRotator(numToKeepStr: '10', daysToKeepStr: '30'))
])

deployApp([
    appType: 'springboot',
    imageName: 'user-service',
    harborUrl: 'harbor.company.com',
    port: 8081,
    
    devServers: ['*********', '*********'],
    qaServers: ['*********'],
    preProdServers: ['*********'],
    prodServers: ['*********', '*********'],
    
    emailRecipients: [
        '<EMAIL>',
        '<EMAIL>'
    ]
])
```

### Basic Angular Pipeline
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

properties([
    buildDiscarder(logRotator(numToKeepStr: '10', daysToKeepStr: '30'))
])

deployApp([
    appType: 'angular',
    imageName: 'customer-portal',
    harborUrl: 'harbor.company.com',
    port: 80,
    
    devServers: ['*********', '*********'],
    qaServers: ['*********'],
    preProdServers: ['*********'],
    prodServers: ['*********', '*********'],
    
    emailRecipients: [
        '<EMAIL>',
        '<EMAIL>'
    ]
])
```

### Advanced Pipeline with Parameters
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

properties([
    buildDiscarder(logRotator(numToKeepStr: '10', daysToKeepStr: '30')),
    parameters([
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: 'Skip running tests'
        ),
        choice(
            name: 'LOG_LEVEL',
            choices: ['INFO', 'DEBUG', 'WARN'],
            description: 'Application log level'
        )
    ])
])

deployApp([
    appType: 'springboot',
    imageName: 'order-service',
    harborUrl: 'harbor.company.com',
    port: 8082,
    
    // Use pipeline parameters
    skipTests: params.SKIP_TESTS,
    
    devServers: ['*********'],
    qaServers: ['*********'],
    preProdServers: ['*********'],
    prodServers: ['*********', '*********'],
    
    emailRecipients: ['<EMAIL>']
])
```

## 🔄 Pipeline Stages Explained

### 1. Checkout Stage
```groovy
stage('Checkout') {
    checkout scm
    echo "✓ Checked out source code from SCM"
}
```
- Pulls latest code from Git repository
- Sets up workspace for build

### 2. Test Stage (Optional)
```groovy
stage('Run Tests') {
    buildUtils.runTests(appType)
}
```
- **Spring Boot**: Runs `mvn test` or `./mvnw test`
- **Angular**: Runs `npm test -- --watch=false --browsers=ChromeHeadless`
- Skipped if `skipTests: true`

### 3. Build Stage
```groovy
stage('Build Application') {
    buildUtils.buildApplication(appType)
}
```
- **Spring Boot**: Runs `mvn clean package -DskipTests`
- **Angular**: Runs `npm install && npm run build --prod`

### 4. Docker Build & Push Stage
```groovy
stage('Build & Push Docker Image') {
    def imageTag = dockerUtils.generateImageTag(env.BRANCH_NAME, env.BUILD_NUMBER)
    dockerUtils.buildAndPushImage(harborUrl, imageName, imageTag)
}
```
- Builds Docker image using Dockerfile
- Tags image based on branch and build number
- Pushes to Harbor registry

### 5. Pull Image Stage
```groovy
stage('Pull Docker Image') {
    dockerUtils.pullImage(servers, fullImageName)
}
```
- Pulls Docker image to target servers
- Authenticates with Harbor registry

### 6. Deploy Stage
```groovy
stage('Deploy to Servers') {
    deployUtils.deployToServers(servers, fullImageName, port)
}
```
- Stops existing containers
- Starts new containers with updated image
- Configures port mapping and restart policy

### 7. Health Check Stage
```groovy
stage('Health Check') {
    def endpoint = (appType == 'springboot') ? '/actuator/health' : '/'
    deployUtils.performHealthCheck(servers, port, endpoint)
}
```
- Waits 30 seconds for application startup
- Performs HTTP health checks
- Retries up to 3 times on failure

## 🚨 Error Handling & Troubleshooting

### Common Build Errors

#### Maven Build Fails
```bash
# Check Java version
java -version

# Check Maven version
mvn -version

# Clean local repository
rm -rf ~/.m2/repository

# Run with debug
mvn clean package -X
```

#### npm Build Fails
```bash
# Check Node version
node --version

# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Docker Build Fails
```bash
# Check Dockerfile syntax
docker build --no-cache -t test-image .

# Check base image availability
docker pull openjdk:17-jre-slim

# Check disk space
df -h
```

### Common Deployment Errors

#### SSH Connection Fails
```bash
# Test SSH connectivity
ssh jenkins@target-server "echo 'SSH works'"

# Check SSH key permissions
chmod 600 ~/.ssh/id_rsa
```

#### Container Start Fails
```bash
# Check container logs
docker logs app-8080

# Check port availability
netstat -tulpn | grep 8080

# Check Docker daemon
systemctl status docker
```

#### Health Check Fails
```bash
# Test health endpoint manually
curl -v http://target-server:8080/actuator/health

# Check application logs
docker logs app-8080

# Check container status
docker ps -a | grep app-8080
```

## 📊 Monitoring & Logging

### Container Status Check
```bash
# Check running containers
docker ps | grep app-

# Check container resource usage
docker stats app-8080

# Check container logs
docker logs -f app-8080
```

### Application Health Monitoring
```bash
# Spring Boot health check
curl http://server:8080/actuator/health

# Angular health check
curl http://server:80/

# Check application metrics (Spring Boot)
curl http://server:8080/actuator/metrics
```

## 🔧 Customization & Extension

### Adding Custom Build Steps
```groovy
// In your Jenkinsfile, add custom stages
pipeline {
    agent any
    stages {
        stage('Custom Pre-Build') {
            steps {
                script {
                    // Your custom logic here
                    sh 'echo "Custom pre-build step"'
                }
            }
        }
        
        stage('Deploy with Shared Library') {
            steps {
                script {
                    deployApp([
                        // Your configuration
                    ])
                }
            }
        }
        
        stage('Custom Post-Deploy') {
            steps {
                script {
                    // Your custom logic here
                    sh 'echo "Custom post-deploy step"'
                }
            }
        }
    }
}
```

### Environment-Specific Configuration
```groovy
// Use different configurations per environment
def getServerConfig(branch) {
    if (branch == 'develop') {
        return [
            servers: ['dev-server-1', 'dev-server-2'],
            port: 8080,
            replicas: 2
        ]
    } else if (branch.startsWith('release/')) {
        return [
            servers: ['qa-server-1'],
            port: 8080,
            replicas: 1
        ]
    } else if (branch.startsWith('tag/')) {
        return [
            servers: ['prod-server-1', 'prod-server-2', 'prod-server-3'],
            port: 8080,
            replicas: 3
        ]
    }
}

def config = getServerConfig(env.BRANCH_NAME)
deployApp([
    appType: 'springboot',
    imageName: 'my-app',
    harborUrl: 'harbor.company.com',
    devServers: config.servers,
    // ... other parameters
])
```

## 📚 Best Practices

### 1. Repository Structure
- Keep Dockerfile in project root
- Include comprehensive README.md
- Use .dockerignore to exclude unnecessary files
- Version your dependencies properly

### 2. Docker Best Practices
- Use multi-stage builds for smaller images
- Run containers as non-root user
- Include health checks in Dockerfile
- Use specific base image tags (not 'latest')

### 3. Pipeline Best Practices
- Use meaningful commit messages
- Tag releases properly (semantic versioning)
- Keep Jenkinsfile simple and readable
- Use parameters for environment-specific values

### 4. Security Best Practices
- Never hardcode secrets in code
- Use Jenkins credentials for sensitive data
- Regularly update base images
- Scan images for vulnerabilities

### 5. Performance Best Practices
- Use Docker layer caching
- Optimize build dependencies
- Monitor resource usage
- Implement proper logging

## 🎯 Migration Guide

### From JAR Deployment
1. Create Dockerfile for your Spring Boot application
2. Update Jenkinsfile to use `deployApp()` function
3. Configure Harbor registry access
4. Test deployment in development environment

### From Manual Deployment
1. Containerize your application
2. Set up Jenkins pipeline
3. Configure target servers with Docker
4. Migrate deployment scripts to Jenkins pipeline

This developer guide provides everything you need to successfully use the Jenkins shared library for your applications! 🚀
