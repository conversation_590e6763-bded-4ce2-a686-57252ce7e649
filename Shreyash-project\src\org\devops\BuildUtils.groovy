package org.devops


 // Build utilities for Spring Boot and Angular applications

class BuildUtils implements Serializable {
    def steps

    BuildUtils(steps) {
        this.steps = steps
    }

    
     // Build application based on type
    
    def buildApplication(String appType) {
        steps.echo "Building ${appType} application..."

        switch (appType.toLowerCase()) {
            case 'springboot':
                buildSpringBoot()
                break
            case 'angular':
                buildAngular()
                break
            default:
                steps.error "Unsupported application type: ${appType}"
        }
    }

    
    // Run tests based on application type
    
    def runTests(String appType) {
        steps.echo "Running tests for ${appType} application..."

        switch (appType.toLowerCase()) {
            case 'springboot':
                runSpringBootTests()
                break
            case 'angular':
                runAngularTests()
                break
            default:
                steps.error "Unsupported application type: ${appType}"
        }
    }

    
     // Build Spring Boot application (standard approach)
    
    private def buildSpringBoot() {
        if (steps.fileExists('mvnw')) {
            steps.sh 'chmod +x mvnw'  // Ensure mvnw is executable
            // Retry build up to 3 times to handle network issues
            steps.retry(3) {
                try {
                    steps.sh './mvnw clean package -DskipTests'
                } catch (Exception e) {
                    steps.echo "Build failed, cleaning Maven cache and retrying..."
                    steps.sh 'rm -rf ~/.m2/repository/net/bytebuddy/ || true'
                    throw e
                }
            }
        } else {
            // Retry build up to 3 times to handle network issues
            steps.retry(3) {
                try {
                    steps.sh 'mvn clean package -DskipTests'
                } catch (Exception e) {
                    steps.echo "Build failed, cleaning Maven cache and retrying..."
                    steps.sh 'rm -rf ~/.m2/repository/net/bytebuddy/ || true'
                    throw e
                }
            }
        }
        steps.echo "Spring Boot build completed"
    }
     // Build Angular application (standard approach)
     
    private def buildAngular() {
        steps.sh 'npm install'
        steps.sh 'npm run build --prod'
        steps.echo "Angular build completed"
    }

    
     // Run Spring Boot tests
     
    private def runSpringBootTests() {
        if (steps.fileExists('mvnw')) {
            steps.sh 'chmod +x mvnw'  // Ensure mvnw is executable
            // Retry tests up to 3 times to handle network issues
            steps.retry(3) {
                steps.sh './mvnw test'
            }
        } else {
            // Retry tests up to 3 times to handle network issues
            steps.retry(3) {
                steps.sh 'mvn test'
            }
        }
        steps.echo "Spring Boot tests completed"
    }

    
     //Run Angular tests
    
    private def runAngularTests() {
        steps.sh 'npm test -- --watch=false --browsers=ChromeHeadless'
        steps.echo "Angular tests completed"
    }

    
    // Run SonarQube analysis (placeholder for future implementation)
    def runSonarAnalysis(String projectKey) {
        steps.echo "SonarQube analysis is currently disabled"
        steps.echo "Project key would be: ${projectKey}"

        // TODO: Implement SonarQube analysis when ready
        // steps.withSonarQubeEnv('SonarQube') {
        //     if (steps.fileExists('mvnw')) {
        //         steps.sh "./mvnw sonar:sonar -Dsonar.projectKey=${projectKey}"
        //     } else if (steps.fileExists('package.json')) {
        //         steps.sh "npm run sonar"
        //     }
        // }
    }
}
