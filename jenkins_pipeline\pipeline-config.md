# Jenkins Pipeline Configuration Guide

This guide helps you configure the Jenkins pipeline for your Spring Boot application using the shared library.

## 🔧 Configuration Steps

### 1. Update Jenkinsfile Environment Variables

Edit the `Jenkinsfile` and update these values according to your environment:

```groovy
environment {
    // Application configuration
    APP_NAME = 'your-app-name'                    // Change to your application name
    APP_TYPE = 'springboot'                       // Keep as 'springboot'
    APP_PORT = '8089'                            // Your application port (from Dockerfile)
    
    // Harbor Registry configuration
    HARBOR_URL = 'your-harbor-registry.com'      // Your Harbor registry URL
    IMAGE_NAME = 'your-project/your-app-name'    // Your Harbor project/image name
    
    // Email notifications
    EMAIL_RECIPIENTS = '<EMAIL>'   // Your team email for notifications
}
```

### 2. Update Server Configurations

In the `deployApp` call, update the server IP addresses:

```groovy
// Server configurations - Replace with your actual server IPs
devServers: [
    '************',    // Your development server IP
    '************'     // Additional dev server (optional)
],
qaServers: [
    '************',    // Your QA server IP
    '************'     // Additional QA server (optional)
],
preProdServers: [
    '************'     // Your pre-production server IP
],
prodServers: [
    '************',    // Your production server IP
    '************'     // Additional prod server (optional)
]
```

### 3. Jenkins Setup Requirements

#### A. Configure Shared Library in Jenkins

1. Go to **Manage Jenkins** → **Configure System**
2. Scroll to **Global Pipeline Libraries**
3. Click **Add** and configure:
   - **Name**: `jenkins-shared-lib`
   - **Default version**: `main` (or your branch name)
   - **Retrieval method**: Modern SCM → Git
   - **Project Repository**: `https://your-gitlab-url/path/to/Shreyash-project.git`
   - **Credentials**: Select your GitLab credentials

#### B. Configure Harbor Registry Credentials

1. Go to **Manage Jenkins** → **Manage Credentials**
2. Select appropriate domain (usually Global)
3. Click **Add Credentials**
4. Choose **Username with password**
5. Configure:
   - **ID**: `harbor-registry-credentials` (must match exactly)
   - **Username**: Your Harbor username
   - **Password**: Your Harbor password
   - **Description**: Harbor Registry Credentials

#### C. Configure SSH Access to Deployment Servers

1. Ensure Jenkins can SSH to your deployment servers
2. Add SSH keys or configure SSH credentials
3. Test SSH connectivity: `ssh jenkins@your-server-ip`

### 4. GitLab Repository Setup

#### A. Create Multibranch Pipeline in Jenkins

1. **New Item** → **Multibranch Pipeline**
2. **Branch Sources** → **Git**
3. **Project Repository**: Your GitLab repository URL
4. **Credentials**: Select your GitLab credentials
5. **Behaviors**: Add "Discover branches" and "Discover tags"

#### B. GitLab Webhook (Optional)

1. In GitLab: **Settings** → **Webhooks**
2. **URL**: `http://your-jenkins-url/git/notifyCommit?url=your-repo-url`
3. **Trigger**: Push events, Tag push events
4. **SSL verification**: Disable if using self-signed certificates

### 5. Branch Strategy

The pipeline automatically handles different branches:

| Branch Pattern | Behavior | Target Servers |
|---------------|----------|----------------|
| `develop` | Full pipeline (test, build, deploy) | Development servers |
| `release/x.y.z` | Full pipeline (test, build, deploy) | QA servers |
| `tag/x.y.z` | Deploy only (uses existing image) | Pre-prod + Production |

### 6. Application Requirements

Ensure your Spring Boot application has:

- ✅ `Dockerfile` (already present)
- ✅ `pom.xml` with proper configuration
- ✅ Health check endpoint (`/actuator/health`)
- ✅ Proper port configuration (8089 in your case)

### 7. Testing the Pipeline

1. **Push to develop branch**: Should deploy to development servers
2. **Create release branch**: `git checkout -b release/1.0.0` → Should deploy to QA
3. **Create tag**: `git tag tag/1.0.0` → Should deploy to production

### 8. Monitoring and Troubleshooting

#### Common Issues:

1. **Harbor login fails**: Check credentials ID matches exactly
2. **SSH connection fails**: Verify SSH keys and connectivity
3. **Docker build fails**: Check Dockerfile and dependencies
4. **Health check fails**: Verify application starts correctly and endpoint is accessible

#### Logs to Check:

- Jenkins console output
- Docker logs on target servers: `docker logs app-8089`
- Application logs within containers

### 9. Optional Configurations

#### Skip Tests (if needed):
```groovy
skipTests: true  // Set to true to skip test execution
```

#### Custom Health Check Endpoint:
The shared library automatically uses `/actuator/health` for Spring Boot applications.

#### Email Notifications:
Ensure SMTP is configured in Jenkins for email notifications to work.

## 🚀 Quick Start Checklist

- [ ] Update Jenkinsfile environment variables
- [ ] Update server IP addresses
- [ ] Configure Jenkins shared library
- [ ] Add Harbor registry credentials
- [ ] Set up SSH access to servers
- [ ] Create multibranch pipeline in Jenkins
- [ ] Test with develop branch
- [ ] Verify deployment on target servers

## 📞 Support

If you encounter issues:
1. Check Jenkins console logs
2. Verify server connectivity
3. Test Docker commands manually
4. Review shared library documentation in `Shreyash-project/README.md`
