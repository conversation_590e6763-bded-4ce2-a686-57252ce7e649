# Jenkins Shared Library for Docker-based DevOps Pipeline

A streamlined Jenkins shared library for Docker-based deployment of Spring Boot and Angular applications using GitLab SCM and Harbor registry.

## Features

- **Branch-based deployment**: Different behavior for `develop`, `release/*`, and `tag/*` branches
- **Multi-application support**: Spring Boot (Java/Maven) and Angular (Node/npm)
- **Docker-only deployment**: Simplified containerized deployment approach
- **Harbor registry integration**: Automated Docker image building and registry management
- **Modular architecture**: Reusable utility classes for build, Docker, and deployment operations
- **Health checks**: Automated post-deployment health verification
- **Email notifications**: Success/failure notifications
- **Test flexibility**: Option to skip tests when needed

## Branch Strategy & Deployment Flow

| Branch Pattern | Checkout | SonarQube* | Test | Build | Push | Pull | Deploy Target | Description |
|---------------|----------|------------|------|-------|------|------|---------------|-------------|
| `develop` | ✅ | ✅* | ✅** | ✅ | ✅ | ✅ | Dev Servers | Full pipeline to development |
| `release/x.y.z` | ✅ | ✅* | ✅** | ✅ | ✅ | ✅ | QA Servers | Full pipeline to QA/test |
| `tag/x.y.z` | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | Pre-prod + Prod | Deploy existing image only |

*SonarQube analysis is commented out for now
**Tests can be skipped with `skipTests: true` parameter

## Library Structure

```
jenkins-shared-lib/
├── vars/
│   └── deployApp.groovy              # Docker-based deployment pipeline (single entry point)
├── src/org/devops/
│   ├── BuildUtils.groovy             # Spring Boot & Angular build utilities
│   ├── DockerUtils.groovy            # Harbor registry & Docker operations
│   ├── DeployUtils.groovy            # Docker deployment & health checks
│   └── NotificationUtils.groovy      # Email notifications
├── resources/
│   └── default-config.yml            # Default configuration values
├── README.md                         # Project overview and quick start
├── SETUP-GUIDE.md                    # Complete setup instructions
└── DEVELOPER-GUIDE.md                # Comprehensive developer guide
```

## Quick Start

### 1. Setup Jenkins Shared Library

1. In Jenkins: **Manage Jenkins** → **Configure System** → **Global Pipeline Libraries**
2. Add library:
   - **Name**: `jenkins-shared-lib`
   - **Default version**: `main`
   - **Retrieval method**: Modern SCM → Git
   - **Project Repository**: Your GitLab repository URL

### 2. Configure Harbor Registry Credentials

1. **Manage Jenkins** → **Manage Credentials**
2. Add **Username with password**:
   - **ID**: `harbor-registry-credentials`
   - **Username**: Your Harbor username
   - **Password**: Your Harbor password

### 3. Create Jenkinsfile

#### Docker-based Deployment (Single Entry Point)
```groovy
@Library('jenkins-shared-lib') _

deployApp([
    appType: 'springboot',              // or 'angular'
    imageName: 'my-app',
    harborUrl: 'harbor.company.com',
    port: 8080,                         // optional, defaults to 8080
    skipTests: false,                   // optional, set to true to skip tests
    devServers: ['*********'],          // for develop branch
    qaServers: ['*********'],           // for release/* branches
    preProdServers: ['*********'],      // for tag/* branches
    prodServers: ['*********'],         // for tag/* branches
    emailRecipients: ['<EMAIL>'] // optional
])
```

#### Branch-based Deployment Behavior
- **develop branch**: Checkout → Tests → Build → Docker Build & Push → Pull → Deploy to `devServers` → Health Check
- **release/x.y.z branch**: Checkout → Tests → Build → Docker Build & Push → Pull → Deploy to `qaServers` → Health Check
- **tag/x.y.z branch**: Pull existing image → Deploy to `preProdServers` + `prodServers` → Health Check

## 📚 Documentation

### Complete Guides
- **[SETUP-GUIDE.md](SETUP-GUIDE.md)** - Complete setup instructions with step-by-step configuration
- **[DEVELOPER-GUIDE.md](DEVELOPER-GUIDE.md)** - Comprehensive developer guide with examples and best practices

### Quick Reference
- **Supported Applications**: Spring Boot (Java/Maven) and Angular (Node.js/npm)
- **Deployment Method**: Docker-based containerized deployment
- **Registry**: Harbor registry for image storage and distribution
- **Branch Strategy**: develop → dev, release/* → qa, tag/* → production

## 🎯 Key Features

- **🐳 Docker-based Deployment**: Containerized applications with Harbor registry
- **🌿 Branch-based Strategy**: Automatic environment detection and deployment
- **🧪 Integrated Testing**: Automated test execution for both Spring Boot and Angular
- **💊 Health Checks**: Post-deployment health verification with retry logic
- **📧 Email Notifications**: Success/failure notifications with detailed information
- **🔒 Security**: Non-root containers, SSH-based deployment, credential management
- **⚡ Performance**: Multi-stage Docker builds, layer caching, parallel operations

## 🚀 Getting Started

### Prerequisites
- Jenkins server with Docker support
- Harbor registry or Docker registry
- Target servers with Docker installed
- SSH access to deployment servers

### Setup Steps
1. **Configure Jenkins**: Set up shared library and credentials
2. **Prepare Applications**: Add Dockerfile and Jenkinsfile to your projects
3. **Configure Servers**: Install Docker and set up SSH access
4. **Test Deployment**: Run your first pipeline

👉 **Follow the complete [SETUP-GUIDE.md](SETUP-GUIDE.md) for detailed instructions**

## 👨‍💻 For Developers

### Supported Application Types
- **Spring Boot**: Java applications with Maven build system
- **Angular**: Frontend applications with npm build system

### Configuration Parameters
#### Required
- `appType`: Application type (`'springboot'` or `'angular'`)
- `imageName`: Docker image name
- `harborUrl`: Harbor registry URL

#### Optional
- `port`: Application port (default: 8080)
- `skipTests`: Skip test execution (default: false)
- `devServers`: Development servers list
- `qaServers`: QA servers list
- `preProdServers`: Pre-production servers list
- `prodServers`: Production servers list
- `emailRecipients`: Email notification recipients

👉 **See [DEVELOPER-GUIDE.md](DEVELOPER-GUIDE.md) for comprehensive examples and best practices**

## 🔧 Architecture

The library consists of modular utility classes:
- **BuildUtils**: Application building and testing
- **DockerUtils**: Docker image creation and Harbor registry operations
- **DeployUtils**: Container deployment and health checks
- **NotificationUtils**: Email notification handling

## 📈 Benefits

- **Consistency**: Standardized deployment process across all applications
- **Reliability**: Automated testing and health checks ensure quality
- **Scalability**: Easy to add new applications and environments
- **Maintainability**: Centralized pipeline logic with modular design
- **Security**: Best practices for container security and credential management

## 🤝 Support

- **Setup Issues**: Check [SETUP-GUIDE.md](SETUP-GUIDE.md)
- **Development Questions**: See [DEVELOPER-GUIDE.md](DEVELOPER-GUIDE.md)
- **Bug Reports**: Create an issue in the repository
- **Feature Requests**: Submit a pull request or create an issue

---

**Ready to deploy? Start with the [SETUP-GUIDE.md](SETUP-GUIDE.md)! 🚀**
