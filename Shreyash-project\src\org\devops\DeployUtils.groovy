package org.devops

class DeployUtils implements Serializable {
    def steps

    DeployUtils(steps) {
        this.steps = steps
    }

    def deployToServers(List servers, String fullImageName, int port = 8080) {
        steps.echo "Deploying ${fullImageName} to ${servers.size()} server(s)"

        servers.each { server ->
            deployToSingleServer(server, fullImageName, port)
        }

        steps.echo "Deployment completed to all servers"
    }

    
    // Deploy to a single server (Docker-based deployment)
    
    private def deployToSingleServer(String server, String fullImageName, int port) {
        steps.echo "Deploying to server: ${server}"

        def containerName = "app-${port}"

        steps.sh """
            ssh -o StrictHostKeyChecking=no jenkins@${server} '
                echo "Stopping and removing existing container..."
                docker stop ${containerName} || true
                docker rm ${containerName} || true

                echo "Starting new container..."
                docker run -d --name ${containerName} -p ${port}:${port} --restart unless-stopped ${fullImageName}

                echo "Container started successfully"
                docker ps | grep ${containerName}
            '
        """

        steps.echo "✓ Successfully deployed to server: ${server}"
    }

    //Perform health check on deployed applications
    def performHealthCheck(List servers, int port = 8080, String endpoint = '/actuator/health') {
        steps.echo "Performing health checks on ${servers.size()} server(s)"

        // Wait for applications to start (configurable in default-config.yml)
        steps.sleep(time: 30, unit: 'SECONDS')

        servers.each { server ->
            healthCheckSingleServer(server, port, endpoint)
        }

        steps.echo "Health checks completed successfully"
    }

    // Health check for a single server
    private def healthCheckSingleServer(String server, int port, String endpoint) {
        steps.echo "Health check for server: ${server}"

        steps.retry(3) { // Retry count configurable in default-config.yml
            steps.sh "curl -f http://${server}:${port}${endpoint}"
        }

        steps.echo "Health check passed for server: ${server}"
    }

    /**
     * Get container status on servers
     */
    def getContainerStatus(List servers) {
        servers.each { server ->
            try {
                def status = steps.sh(
                    script: "ssh -o StrictHostKeyChecking=no jenkins@${server} 'docker ps --filter name=app --format \"table {{.Names}}\\t{{.Status}}\\t{{.Ports}}\"'",
                    returnStdout: true
                ).trim()

                steps.echo "Container status on ${server}:\n${status}"
            } catch (Exception e) {
                steps.echo "Failed to get container status from ${server}: ${e.getMessage()}"
            }
        }
    }
}
