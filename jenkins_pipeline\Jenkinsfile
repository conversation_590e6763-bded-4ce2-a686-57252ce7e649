@Library('jenkins-shared-lib') _

/**
 * Jenkins Pipeline for Spring Boot Application
 * Uses shared library from Shreyash-project for Docker-based deployment
 * 
 * This pipeline supports:
 * - develop branch: Deploy to development servers
 * - release/* branches: Deploy to QA servers  
 * - tag/* branches: Deploy to pre-production and production servers
 */

pipeline {
    agent any
    
    environment {
        // Application configuration
        APP_NAME = 'spring-boot-demo'
        APP_TYPE = 'springboot'
        APP_PORT = '8089'  // Based on your Dockerfile EXPOSE port
        
        // Harbor Registry configuration
        HARBOR_URL = 'harbor.techvgi.com'       // Your Harbor registry URL
        IMAGE_NAME = 'techvg/spring-boot-demo'  // Your Harbor project/image name
        
        // Email notifications (optional)
        EMAIL_RECIPIENTS = '<EMAIL>'  // Replace with your team email
    }
    
    options {
        // Keep builds for 30 days or last 10 builds
        buildDiscarder(logRotator(daysToKeepStr: '30', numToKeepStr: '10'))
        
        // Timeout the build after 30 minutes
        timeout(time: 30, unit: 'MINUTES')
        
        // Skip default checkout - handled by shared library
        skipDefaultCheckout(true)
        
        // Add timestamps to console output
        timestamps()
    }
    
    stages {
        stage('Pipeline Info') {
            steps {
                script {
                    echo "=== Pipeline Information ==="
                    echo "Application: ${APP_NAME}"
                    echo "Type: ${APP_TYPE}"
                    echo "Port: ${APP_PORT}"
                    echo "Branch: ${env.BRANCH_NAME}"
                    echo "Build Number: ${env.BUILD_NUMBER}"
                    echo "Harbor URL: ${HARBOR_URL}"
                    echo "Image Name: ${IMAGE_NAME}"
                    echo "=============================="
                }
            }
        }
        
        stage('Deploy Application') {
            steps {
                script {
                    // Call the shared library deployApp function
                    deployApp([
                        // Required parameters
                        appType: "${APP_TYPE}",
                        imageName: "${IMAGE_NAME}",
                        harborUrl: "${HARBOR_URL}",
                        
                        // Optional parameters
                        port: "${APP_PORT}" as Integer,
                        skipTests: false,  // Set to true if you want to skip tests
                        
                        // Server configurations (update with your actual server IPs)
                        devServers: [
                            '*********',    // Development server 1
                            '*********'     // Development server 2 (optional)
                        ],
                        qaServers: [
                            '*********',    // QA server 1
                            '*********'     // QA server 2 (optional)
                        ],
                        preProdServers: [
                            '*********'     // Pre-production server
                        ],
                        prodServers: [
                            '*********',    // Production server 1
                            '*********'     // Production server 2 (optional)
                        ],
                        
                        // Email notifications (optional) - must be a List
                        emailRecipients: ["${EMAIL_RECIPIENTS}"]
                    ])
                }
            }
        }
    }
    
    post {
        always {
            script {
                echo "=== Pipeline Completed ==="
                echo "Status: ${currentBuild.result ?: 'SUCCESS'}"
                echo "Duration: ${currentBuild.durationString}"
                echo "=========================="
            }
        }
        
        success {
            script {
                echo "🎉 Pipeline completed successfully!"
                echo "Application ${APP_NAME} has been deployed successfully."
            }
        }
        
        failure {
            script {
                echo "❌ Pipeline failed!"
                echo "Check the logs above for error details."
            }
        }
        
        unstable {
            script {
                echo "⚠️ Pipeline completed with warnings."
            }
        }
        
        cleanup {
            // Clean up workspace after build
            cleanWs(
                cleanWhenAborted: true,
                cleanWhenFailure: true,
                cleanWhenNotBuilt: true,
                cleanWhenSuccess: true,
                cleanWhenUnstable: true,
                deleteDirs: true
            )
        }
    }
}
