# 🚀 Jenkins Shared Library - Complete Setup Guide

This guide provides step-by-step instructions to set up the Jenkins shared library for Docker-based deployment of Spring Boot and Angular applications.

## 📋 Prerequisites

### Infrastructure Requirements
- **Jenkins Server** (2.400+) with Docker support
- **Harbor Registry** or Docker registry
- **Target Servers** with Docker installed
- **GitLab/GitHub** repository access
- **SSH Access** to target servers

### Required Jenkins Plugins
```bash
# Install these plugins in Jenkins
- Pipeline: Shared Groovy Libraries
- Docker Pipeline
- Git
- SSH Agent
- Credentials Binding
- Email Extension
- Build Timeout
- Timestamper
```

## 🔧 Step 1: Jenkins Global Configuration

### 1.1 Configure Shared Library
1. Go to **Manage Jenkins** → **Configure System**
2. Scroll to **Global Pipeline Libraries**
3. Click **Add** and configure:
   ```
   Name: jenkins-shared-lib
   Default version: main
   Retrieval method: Modern SCM
   Source Code Management: Git
   Project Repository: https://gitlab.company.com/devops/jenkins-shared-lib.git
   Credentials: [Select your Git credentials]
   ```
4. Check **Load implicitly** and **Allow default version to be overridden**

### 1.2 Configure Global Tools
1. **Maven Configuration**:
   - Go to **Manage Jenkins** → **Global Tool Configuration**
   - Add Maven installation: `Maven-3.9`
   - Install automatically from Apache

2. **Node.js Configuration**:
   - Add Node.js installation: `NodeJS-18`
   - Install automatically from nodejs.org

3. **Docker Configuration**:
   - Add Docker installation: `Docker-Latest`
   - Install automatically from docker.com

## 🔐 Step 2: Credentials Setup

### 2.1 Harbor Registry Credentials
1. Go to **Manage Jenkins** → **Manage Credentials**
2. Select **Global** domain
3. Click **Add Credentials**
4. Configure:
   ```
   Kind: Username with password
   Scope: Global
   Username: [Harbor username]
   Password: [Harbor password]
   ID: harbor-registry-credentials
   Description: Harbor Registry Access
   ```

### 2.2 SSH Credentials for Target Servers
1. Add SSH credentials for deployment servers:
   ```
   Kind: SSH Username with private key
   Scope: Global
   Username: jenkins
   Private Key: [Upload your private key]
   ID: deployment-ssh-key
   Description: SSH access to deployment servers
   ```

### 2.3 Email Credentials (Optional)
1. For email notifications:
   ```
   Kind: Username with password
   Scope: Global
   Username: [SMTP username]
   Password: [SMTP password]
   ID: smtp-credentials
   Description: SMTP Email Access
   ```

## 🐳 Step 3: Harbor Registry Setup

### 3.1 Create Harbor Projects
1. Login to Harbor web interface
2. Create projects for your applications:
   ```
   Project Name: my-spring-app
   Access Level: Private
   ```

### 3.2 Create Robot Account
1. Go to **Projects** → **[Your Project]** → **Robot Accounts**
2. Click **New Robot Account**
3. Configure:
   ```
   Name: jenkins-deploy
   Expiration: Never expires
   Permissions: Push, Pull
   ```
4. Save the generated token for Jenkins credentials

## 🖥️ Step 4: Target Server Setup

### 4.1 Install Docker on Target Servers
```bash
# On each target server (Ubuntu/Debian)
sudo apt update
sudo apt install -y docker.io
sudo systemctl start docker
sudo systemctl enable docker

# Add jenkins user to docker group
sudo usermod -aG docker jenkins
```

### 4.2 Configure SSH Access
```bash
# On Jenkins server, generate SSH key if not exists
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# Copy public key to target servers
ssh-copy-id jenkins@*********  # Dev server
ssh-copy-id jenkins@*********  # QA server
ssh-copy-id jenkins@*********  # Prod server

# Test SSH access
ssh jenkins@********* "docker --version"
```

### 4.3 Configure Docker Login on Target Servers
```bash
# On each target server, login to Harbor
docker login harbor.company.com
# Enter Harbor credentials when prompted

# Verify login
docker pull hello-world
```

## 📁 Step 5: Project Structure Setup

### 5.1 Create Application Repository
```bash
# Create new repository structure
my-spring-app/
├── src/
├── pom.xml
├── Dockerfile
├── Jenkinsfile
└── README.md
```

### 5.2 Sample Dockerfile for Spring Boot
```dockerfile
# Dockerfile
FROM openjdk:17-jre-slim

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY target/*.jar app.jar

EXPOSE 8080

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 5.3 Sample Dockerfile for Angular
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build --prod

FROM nginx:alpine
RUN apk add --no-cache curl
COPY --from=builder /app/dist/* /usr/share/nginx/html/
EXPOSE 80
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:80/ || exit 1
CMD ["nginx", "-g", "daemon off;"]
```

## 🔄 Step 6: Pipeline Configuration

### 6.1 Basic Jenkinsfile for Spring Boot
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

properties([
    buildDiscarder(logRotator(numToKeepStr: '10', daysToKeepStr: '30'))
])

deployApp([
    appType: 'springboot',
    imageName: 'my-spring-app',
    harborUrl: 'harbor.company.com',
    port: 8080,
    
    // Environment-specific servers
    devServers: ['*********', '*********'],
    qaServers: ['*********'],
    preProdServers: ['*********'],
    prodServers: ['*********', '*********'],
    
    // Notification settings
    emailRecipients: ['<EMAIL>', '<EMAIL>']
])
```

### 6.2 Basic Jenkinsfile for Angular
```groovy
#!/usr/bin/env groovy
@Library('jenkins-shared-lib') _

properties([
    buildDiscarder(logRotator(numToKeepStr: '10', daysToKeepStr: '30'))
])

deployApp([
    appType: 'angular',
    imageName: 'my-angular-app',
    harborUrl: 'harbor.company.com',
    port: 80,
    
    // Environment-specific servers
    devServers: ['*********', '*********'],
    qaServers: ['*********'],
    preProdServers: ['*********'],
    prodServers: ['*********', '*********'],
    
    // Notification settings
    emailRecipients: ['<EMAIL>', '<EMAIL>']
])
```

## 📧 Step 7: Email Configuration (Optional)

### 7.1 Configure SMTP in Jenkins
1. Go to **Manage Jenkins** → **Configure System**
2. Scroll to **E-mail Notification**
3. Configure:
   ```
   SMTP server: smtp.company.com
   Default user e-mail suffix: @company.com
   Use SMTP Authentication: Yes
   User Name: [SMTP username]
   Password: [SMTP password]
   Use SSL: Yes
   SMTP Port: 465
   ```

### 7.2 Test Email Configuration
1. Check **Test configuration by sending test e-mail**
2. Enter test email address
3. Click **Test configuration**

## 🧪 Step 8: Testing the Setup

### 8.1 Create Test Pipeline
1. Create new **Pipeline** job in Jenkins
2. Configure pipeline script from SCM
3. Point to your application repository
4. Save and build

### 8.2 Verify Pipeline Stages
The pipeline should execute these stages:
```
✅ Checkout
✅ Run Tests
✅ Build Application  
✅ Build & Push Docker Image
✅ Pull Docker Image
✅ Deploy to Servers
✅ Health Check
✅ Post-Deploy Status
```

### 8.3 Verify Deployment
```bash
# Check container on target server
ssh jenkins@********* "docker ps | grep app-8080"

# Check application health
curl http://*********:8080/actuator/health  # Spring Boot
curl http://*********:80/                   # Angular
```

## 🔍 Step 9: Troubleshooting

### 9.1 Common Issues

#### Pipeline Fails at Checkout
```bash
# Check Git credentials
# Verify repository URL
# Ensure Jenkins has access to repository
```

#### Docker Build Fails
```bash
# Check Dockerfile syntax
# Verify base images are accessible
# Check Docker daemon on Jenkins agent
```

#### Deployment Fails
```bash
# Verify SSH connectivity
ssh jenkins@target-server "echo 'SSH works'"

# Check Docker on target servers
ssh jenkins@target-server "docker --version"

# Verify Harbor access
ssh jenkins@target-server "docker pull harbor.company.com/hello-world"
```

#### Health Check Fails
```bash
# Check application logs
ssh jenkins@target-server "docker logs app-8080"

# Verify health endpoint
curl -v http://target-server:8080/actuator/health
```

### 9.2 Debug Commands
```bash
# Check Jenkins logs
tail -f /var/log/jenkins/jenkins.log

# Check Docker images on Jenkins agent
docker images | grep harbor.company.com

# Check running containers on target servers
ssh jenkins@target-server "docker ps -a"

# Check Harbor registry
curl -u username:password https://harbor.company.com/v2/_catalog
```

## ✅ Step 10: Validation Checklist

- [ ] Jenkins shared library configured and loaded
- [ ] All required plugins installed
- [ ] Harbor registry credentials configured
- [ ] SSH access to target servers working
- [ ] Docker installed and configured on all servers
- [ ] Sample application repository created
- [ ] Dockerfile created and tested
- [ ] Jenkinsfile configured with correct parameters
- [ ] Pipeline runs successfully end-to-end
- [ ] Application accessible on target servers
- [ ] Health checks passing
- [ ] Email notifications working (if configured)

## 🎯 Next Steps

1. **Read the Developer Guide** for detailed usage instructions
2. **Customize the pipeline** for your specific applications
3. **Set up monitoring** and alerting for production deployments
4. **Configure backup and disaster recovery** procedures
5. **Implement security scanning** in the pipeline

Your Jenkins shared library is now ready for production use! 🚀
